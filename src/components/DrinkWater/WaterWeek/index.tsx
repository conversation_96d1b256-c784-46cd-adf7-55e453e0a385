import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Toast } from '@xmly/rn-sdk';
import { getStyles } from './styles';
import type { WaterWeek } from 'services/welfare/drinkWater';
import watchAd from 'utils/watchAd';
import useRewardGoldCoin from 'hooks/useRewardGoldCoin';
import { AD_SOURCE, RewardType, LISTEN_TASK_POSITION, FallbackReqType } from 'constants/ad';

// 假设已有的广告和奖励服务，如果不存在则需要创建
// import { watchAd } from 'services/ad';
// import { rewardGoldCoin } from 'services/welfare/reward';

interface WaterWeekProps {
  waterWeek: WaterWeek;
  onRefresh: () => void;
}

export default function WaterWeekCard({ waterWeek, onRefresh }: WaterWeekProps) {
  const styles = getStyles();
  const { continueDrinkDay, coins, status, dayDrinkNums, title, tips } = waterWeek;
  const rewardGoldCoin = useRewardGoldCoin();

  const handleReward = async () => {
    if (status === 1) return; // 已领取

    try {
      // 1. 调用看广告方法，参照 WaterGlass.tsx 的补打卡逻辑
      const adResult = await watchAd({
        // TODO: 请产品和开发确认新的广告点位和奖励类型
        sourceName: AD_SOURCE.DRINK_WATER_WEEKLY_REWARD, // 假设的广告来源
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.DRINK_WATER_WEEKLY_REWARD, // 假设的奖励类型
        coins: waterWeek.coins,
        rewardVideoStyle: 0,
      });

      if (adResult.success) {
        // 2. 发放奖励
        const result = await rewardGoldCoin({
          rewardType: RewardType.DRINK_WATER_WEEKLY_REWARD,
          sourceName: AD_SOURCE.DRINK_WATER_WEEKLY_REWARD,
          coins: waterWeek.coins,
          adId: adResult.adId,
          adResponseId: adResult.adResponseId,
          encryptType: adResult.encryptType,
          ecpm: adResult.ecpm,
          fallbackReq: adResult.fallbackReq ?? FallbackReqType.NORMAL,
        });

        if (result?.success) {
          // 3. 刷新数据
          Toast.info(`恭喜你获得${result.coins}金币`);
          onRefresh();
        } else {
          Toast.info('领取奖励失败，请稍后重试');
        }
      }
    } catch (error) {
      console.error('领取奖励失败', error);
      Toast.info('领取奖励失败，请稍后重试');
    }
  };

  const renderDayItem = (dayIndex: number) => {
    const day = dayIndex + 1;
    const drinks = dayDrinkNums[dayIndex] || 0;
    const isCompleted = day <= continueDrinkDay;

    // 第七天特殊处理
    if (day === 7) {
      const isDay7Completed = continueDrinkDay >= 7 && drinks > 0;
      const canClaim = isDay7Completed && status === 0;
      const imageSource = isDay7Completed 
        ? require('./images/drink_7_coin_complete.png') 
        : require('./images/drink_7_coin_uncomplete.png');
      
      return (
        <TouchableOpacity key={day} style={styles.dayContainer} onPress={handleReward} disabled={!canClaim}>
          <Image source={imageSource} style={[styles.dayImage, status === 1 && styles.claimedImage]} />
          <Text style={[styles.dayText, isCompleted && styles.dayTextCompleted]}>{day}天</Text>
        </TouchableOpacity>
      );
    }

    // 前六天
    const waterLevel = Math.min(drinks, 8); // 最多8个等级
    let imageSource;
    if (isCompleted && waterLevel > 0) {
      // 假设图片命名为 drink_water_1.png, drink_water_2.png ...
      // 这里需要动态加载，RN Metro 不直接支持动态字符串，所以我们用 require case
      switch (waterLevel) {
        case 1: imageSource = require('./images/drink_water_1.png'); break;
        case 2: imageSource = require('./images/drink_water_2.png'); break;
        case 3: imageSource = require('./images/drink_water_3.png'); break;
        case 4: imageSource = require('./images/drink_water_4.png'); break;
        case 5: imageSource = require('./images/drink_water_5.png'); break;
        case 6: imageSource = require('./images/drink_water_6.png'); break;
        case 7: imageSource = require('./images/drink_water_7.png'); break;
        case 8: imageSource = require('./images/drink_water_8.png'); break;
        default: imageSource = require('./images/drink_water_0.png'); // 未打卡时的默认空杯
      }
    } else {
        imageSource = require('./images/drink_water_0.png');
    }

    return (
      <View key={day} style={styles.dayContainer}>
        <Image source={imageSource} style={styles.dayImage} />
        <Text style={[styles.dayText, isCompleted && styles.dayTextCompleted]}>{day}天</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subTitle}>已打卡{continueDrinkDay}天</Text>
      </View>

      <View style={styles.daysWrapper}>
        {/* Progress Bar */}
        <View style={styles.progressBarContainer}>
            <View style={[styles.progressBar, { width: `${(continueDrinkDay / 7) * 100}%` }]} />
        </View>
        
        {/* Days */}
        <View style={styles.daysContainer}>
          {Array.from({ length: 7 }).map((_, index) => renderDayItem(index))}
        </View>
      </View>
      
      <Text style={styles.tips}>{tips}</Text>
    </View>
  );
} 