import { StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');
const containerPadding = 16;
const dayItemMargin = 8;
const dayItemWidth = (width - 2 * containerPadding - 6 * dayItemMargin) / 7;

export const getStyles = () => StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: containerPadding,
    marginHorizontal: 16,
    marginTop: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  subTitle: {
    fontSize: 14,
    color: '#666',
  },
  daysWrapper: {
    position: 'relative',
    height: 80, 
    justifyContent: 'center',
    marginBottom: 8,
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    zIndex: 2,
  },
  progressBarContainer: {
    position: 'absolute',
    left: dayItemWidth / 2,
    right: dayItemWidth / 2,
    top: '60%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    zIndex: 1,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4A90E2',
    borderRadius: 2,
  },
  dayContainer: {
    alignItems: 'center',
    width: dayItemWidth,
  },
  dayImage: {
    width: dayItemWidth * 0.8,
    height: dayItemWidth * 0.8,
    resizeMode: 'contain',
    marginBottom: 8,
  },
  claimedImage: {
    opacity: 0.7,
  },
  dayText: {
    fontSize: 12,
    color: '#999',
  },
  dayTextCompleted: {
    color: '#4A90E2',
    fontWeight: '500',
  },
  tips: {
    fontSize: 12,
    color: '#999999',
    marginTop: 8,
  },
}); 