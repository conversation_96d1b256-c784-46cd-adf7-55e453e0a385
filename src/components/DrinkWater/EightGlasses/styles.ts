import { StyleSheet } from 'react-native';
import { px } from 'utils/px';

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      backgroundColor: '#FFFFFF',
      marginHorizontal: px(16),
      marginTop: px(16),
      borderRadius: px(12),
      paddingHorizontal: px(12),
      paddingVertical: px(24),
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: px(2),
      },
      shadowOpacity: 0.1,
      shadowRadius: px(4),
      elevation: 3,
    },
    glassesContainer: {
      // gap 在老版本RN中不支持，使用 marginBottom 替代
    },
    row: {
      flexDirection: 'row',
      marginBottom: px(16),
      justifyContent: 'flex-start',
    },
    lastRow: {
      marginBottom: px(0),
    },
    glassWrapper: {
      // 固定宽度，不使用 flex
    },
    glassMarginRight: {
      marginRight: px(12),
    },
    loadingContainer: {
      height: px(200),
      alignItems: 'center',
      justifyContent: 'center',
    },
    loadingText: {
      fontSize: px(14),
      color: '#999999',
    },
    errorContainer: {
      height: px(200),
      alignItems: 'center',
      justifyContent: 'center',
    },
    errorText: {
      fontSize: px(14),
      color: '#999999',
    },
  });
};
