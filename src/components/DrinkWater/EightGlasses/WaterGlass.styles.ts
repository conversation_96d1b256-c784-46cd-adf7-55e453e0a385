import { StyleSheet, Dimensions } from 'react-native';
import { px } from 'utils/px';

export const getStyles = () => {
  const screenWidth = Dimensions.get('window').width;
  const glassWidth = (screenWidth - px(92)) / 4;
  const glassHeight = (93 / 70) * glassWidth;

  return StyleSheet.create({
    container: {
      width: glassWidth,
      height: glassHeight,
      borderRadius: px(8),
      overflow: 'hidden',
    },
    backgroundImage: {
      width: '100%',
      height: '100%',
      justifyContent: 'flex-start',
      alignItems: 'center',
    },
    content: {
      width: '100%',
      height: '100%',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    title: {
      fontSize: px(12),
      fontWeight: '600',
      color: '#3979B9',
      textAlign: 'center',
      marginTop: px(12),
    },
    bottomText: {
      fontSize: px(12),
      fontWeight: '600',
      marginBottom: px(4),
    },
    // 已打卡状态文案
    completedText: {
      color: '#4A85BB',
      textAlign: 'center',
    },
    // 补打卡状态文案
    makeUpText: {
      color: '#FFFFFF',
      textAlign: 'center',
    },
    // 待打卡/未开始状态文案（金币）
    coinsText: {
      color: '#FFFFFF',
      marginLeft: px(22),
      alignSelf: 'flex-start',
      marginBottom: px(2),
    },
    // 蒙层样式
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    // 时间区域文案样式
    timeAreaText: {
      fontSize: px(14),
      color: '#FFFFFF',
      fontWeight: '600',
      textAlign: 'center',
    },

  });
};
