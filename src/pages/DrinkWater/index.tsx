import React, { useEffect } from "react";
import { View, ScrollView, Platform, NativeModules } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList, RootNavigationProps } from "router/type";
import { usePageReport } from "hooks/usePageReport";
import { getStyles } from "./style";
import Header from "components/DrinkWater/Header";
import EightGlasses from "components/DrinkWater/EightGlasses";
import WaterTip from "components/DrinkWater/WaterTip";
import WaterWeekCard from "components/DrinkWater/WaterWeek";
import { PageEventEmitter } from "defs";
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
import { useAtomValue, useSetAtom } from "jotai";
import { waterInfoAtom, writeWaterInfoAtom } from "components/DrinkWater/EightGlasses/store";

export default function DrinkWater(props: StackScreenProps<RootStackParamList>) {
  const styles = getStyles();
  const navigation = useNavigation<RootNavigationProps>();

  const waterInfo = useAtomValue(waterInfoAtom);
  const fetchWaterInfo = useSetAtom(writeWaterInfoAtom);

  usePageReport({
    pageViewCode: 67685, // 需要替换为实际的页面埋点代码
    pageExitCode: 67686, // 需要替换为实际的页面埋点代码
    currPage: 'drinkWater',
    otherProps: props
  });

  useEffect(() => {
    // 去掉骨架屏
    GlobalEventEmitter.emit('appContentReady');

    const blurListener = navigation.addListener('blur', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
      }
    });

    const focusListener = navigation.addListener('focus', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(true)
      }
    });

    const resumeListener = PageEventEmitter.addListener('onResume', () => {
      console.log("DrinkWater onResuming....................")
      // 可以在这里刷新数据
      fetchWaterInfo();
    });

    return () => {
      blurListener?.();
      focusListener?.();
      resumeListener?.remove();
    }
  }, [navigation]);

  function navigateBack() {
    navigation.goBack();
  }

  return (
    <View style={styles.container}>
      <Header onBackPress={navigateBack} />
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <EightGlasses />
        <WaterTip />
        {waterInfo?.waterWeek && (
          <WaterWeekCard 
            waterWeek={waterInfo.waterWeek}
            onRefresh={fetchWaterInfo}
          />
        )}
      </ScrollView>
    </View>
  );
}
